import SwiftUI
import UIKit
import AVFoundation

// MARK: - Camera View
struct CameraView: UIViewControllerRepresentable {
    @Binding var capturedImage: UIImage?
    @Binding var isPresented: Bool
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .camera
        picker.allowsEditing = false
        picker.cameraDevice = .rear
        
        // Check if camera is available
        if !UIImagePickerController.isSourceTypeAvailable(.camera) {
            // Handle camera not available case
            DispatchQueue.main.async {
                self.isPresented = false
            }
        }
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {
        // No updates needed for this implementation
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    // MARK: - Coordinator
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraView
        
        init(_ parent: CameraView) {
            self.parent = parent
        }
        
        // MARK: - Image Picker Delegate Methods
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            
            // Extract the captured image
            if let image = info[.originalImage] as? UIImage {
                DispatchQueue.main.async {
                    self.parent.capturedImage = image
                    print("📸 照片拍摄成功，图像尺寸: \(image.size)")
                    
                    // Dismiss the camera
                    self.parent.isPresented = false
                }
            } else {
                print("❌ 无法获取拍摄的图像")
                DispatchQueue.main.async {
                    self.parent.isPresented = false
                }
            }
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            print("📷 用户取消了拍照")
            parent.isPresented = false
        }
        
        // MARK: - Error Handling
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any], error: Error?) {
            if let error = error {
                print("❌ 拍照过程中发生错误: \(error.localizedDescription)")
            }
            parent.isPresented = false
        }
    }
}

// MARK: - Camera Availability Helper
extension CameraView {
    static func isCameraAvailable() -> Bool {
        return UIImagePickerController.isSourceTypeAvailable(.camera)
    }
    
    static func getCameraAuthorizationStatus() -> AVAuthorizationStatus {
        return AVCaptureDevice.authorizationStatus(for: .video)
    }
}