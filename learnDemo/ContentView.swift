import SwiftUI
import simd

// MARK: - Mock数据模型
struct MockItem: Identifiable {
    let id = UUID()
    let title: String
    let subtitle: String
    let index: Int
}

struct ContentView: View {
   
    // 定义一个 4x4 颜色变换矩阵（示例：灰度滤镜）
    var colorMatrix: simd_float4x4 {
        return simd_float4x4(
            simd_float4(0.299, 0.587, 0.114, 0.0),  // 红色通道
            simd_float4(0.299, 0.587, 0.114, 0.0),  // 绿色通道
            simd_float4(0.299, 0.587, 0.114, 0.0),  // 蓝色通道
            simd_float4(0.0,   0.0,   0.0,   1.0)   // Alpha 通道
        )
    }
    
    var body: some View {
        Text("hello").frame(width: 100, height: 100)
            .foregroundColor(.yellow)
            .colorEffect(ShaderLibrary.colorMatrix(.simdFloat4x4(colorMatrix)))
    }
    
}
