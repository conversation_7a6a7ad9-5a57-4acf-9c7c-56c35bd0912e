//
//  ColorMatrix.metal
//  learnDemo
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/27.
//

#include <metal_stdlib>
using namespace metal;

[[stitchable]] half4 colorMatrix(float2 position, half4 color, float4x4 matrix) {
    // 将输入颜色转换为向量
    half4 inputColor = color;
    
    // 应用颜色变换矩阵
    half4 outputColor;
    outputColor.r = matrix[0][0] * inputColor.r + matrix[0][1] * inputColor.g + matrix[0][2] * inputColor.b + matrix[0][3] * inputColor.a;
    outputColor.g = matrix[1][0] * inputColor.r + matrix[1][1] * inputColor.g + matrix[1][2] * inputColor.b + matrix[1][3] * inputColor.a;
    outputColor.b = matrix[2][0] * inputColor.r + matrix[2][1] * inputColor.g + matrix[2][2] * inputColor.b + matrix[2][3] * inputColor.a;
    outputColor.a = matrix[3][0] * inputColor.r + matrix[3][1] * inputColor.g + matrix[3][2] * inputColor.b + matrix[3][3] * inputColor.a;
    
    return outputColor;
}
