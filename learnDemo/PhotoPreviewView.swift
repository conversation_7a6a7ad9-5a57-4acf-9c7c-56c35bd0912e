import SwiftUI

// MARK: - Photo Preview View
struct PhotoPreviewView: View {
    let capturedImage: UIImage
    @Binding var isPresented: Bool
    let onRetake: () -> Void
    let onKeep: () -> Void
    
    var body: some View {
        NavigationStack {
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 图片预览区域
                    ZStack {
                        Color.black
                            .ignoresSafeArea()
                        
                        Image(uiImage: capturedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .clipped()
                    }
                    .frame(maxHeight: .infinity)
                    
                    // 控制按钮区域
                    VStack(spacing: 16) {
                        // 图片信息
                        VStack(alignment: .leading, spacing: 4) {
                            Text("照片预览")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text("尺寸: \(Int(capturedImage.size.width)) × \(Int(capturedImage.size.height))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // 操作按钮
                        HStack(spacing: 20) {
                            Button(action: {
                                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                impactFeedback.impactOccurred()
                                print("🔄 用户选择重拍")
                                onRetake()
                            }) {
                                HStack(spacing: 8) {
                                    Image(systemName: "camera.rotate")
                                        .font(.system(size: 16, weight: .medium))
                                    Text("重拍")
                                        .font(.system(size: 16, weight: .medium))
                                }
                                .frame(maxWidth: .infinity)
                                .frame(minHeight: 50)
                                .background(Color(.systemGray5))
                                .foregroundColor(.primary)
                                .cornerRadius(12)
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            Button(action: {
                                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                impactFeedback.impactOccurred()
                                print("✅ 用户选择保留照片")
                                onKeep()
                            }) {
                                HStack(spacing: 8) {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 16, weight: .medium))
                                    Text("保留")
                                        .font(.system(size: 16, weight: .medium))
                                }
                                .frame(maxWidth: .infinity)
                                .frame(minHeight: 50)
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(12)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .padding(.horizontal)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                }
            }
            .navigationTitle("照片预览")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        print("❌ 用户取消预览")
                        isPresented = false
                    }
                }
            }
        }
    }
}

// MARK: - Preview Helper
#if DEBUG
struct PhotoPreviewView_Previews: PreviewProvider {
    static var previews: some View {
        // Create a sample image for preview
        let sampleImage = UIImage(systemName: "photo") ?? UIImage()
        
        PhotoPreviewView(
            capturedImage: sampleImage,
            isPresented: .constant(true),
            onRetake: {
                print("Preview: Retake tapped")
            },
            onKeep: {
                print("Preview: Keep tapped")
            }
        )
    }
}
#endif