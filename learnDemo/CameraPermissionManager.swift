import Foundation
import AVFoundation
import UIKit

// MARK: - Permission Error Types
enum PermissionError: LocalizedError {
    case denied
    case restricted
    case unknown
    
    var errorDescription: String? {
        switch self {
        case .denied:
            return "摄像头权限被拒绝，请在设置中启用"
        case .restricted:
            return "摄像头访问受限"
        case .unknown:
            return "未知权限错误"
        }
    }
}

// MARK: - Camera State
enum CameraState {
    case idle           // 空闲状态
    case requesting     // 请求权限中
    case authorized     // 已授权
    case denied         // 权限被拒绝
    case restricted     // 权限受限
    case capturing      // 拍照中
    case previewing     // 预览中
}

// MARK: - Camera Permission Manager
class CameraPermissionManager: ObservableObject {
    @Published var permissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var cameraState: CameraState = .idle
    
    init() {
        checkPermissionStatus()
    }
    
    // MARK: - Permission Status Check
    @discardableResult
    func checkPermissionStatus() -> AVAuthorizationStatus {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        DispatchQueue.main.async {
            self.permissionStatus = status
            self.updateCameraState(for: status)
        }
        return status
    }
    
    // MARK: - Request Permission
    func requestPermission() async -> Bool {
        await MainActor.run {
            cameraState = .requesting
        }
        
        let granted = await AVCaptureDevice.requestAccess(for: .video)
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        
        await MainActor.run {
            self.permissionStatus = status
            self.updateCameraState(for: status)
        }
        
        return granted
    }
    
    // MARK: - Open Settings
    func openSettings() {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    // MARK: - Camera Availability Check
    func isCameraAvailable() -> Bool {
        return UIImagePickerController.isSourceTypeAvailable(.camera)
    }
    
    // MARK: - Permission Error
    func getPermissionError() -> PermissionError? {
        switch permissionStatus {
        case .denied:
            return .denied
        case .restricted:
            return .restricted
        case .authorized, .notDetermined:
            return nil
        @unknown default:
            return .unknown
        }
    }
    
    // MARK: - Private Methods
    private func updateCameraState(for status: AVAuthorizationStatus) {
        switch status {
        case .authorized:
            cameraState = .authorized
        case .denied:
            cameraState = .denied
        case .restricted:
            cameraState = .restricted
        case .notDetermined:
            cameraState = .idle
        @unknown default:
            cameraState = .idle
        }
    }
}