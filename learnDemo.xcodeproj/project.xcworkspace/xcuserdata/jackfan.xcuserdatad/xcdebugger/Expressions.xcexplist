<?xml version="1.0" encoding="UTF-8"?>
<VariablesViewState
   version = "1.0">
   <ContextStates>
      <ContextState
         contextName = "closure #2 in closure #1 in closure #1 in closure #1 in ContentView.body.getter:ContentView.swift">
         <PersistentStrings>
            <PersistentString
               value = "">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "closure #1 in closure #2 in closure #1 in closure #1 in ContentView.body.getter:ContentView.swift">
         <PersistentStrings>
            <PersistentString
               value = "scrollGeometry">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
   </ContextStates>
</VariablesViewState>
