// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		17321CAA2E35E92700A3F272 /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 17321CA92E35E92700A3F272 /* Localizable.xcstrings */; };
		17321CB42E35F53300A3F272 /* InfoPlist.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 17321CB32E35F53300A3F272 /* InfoPlist.xcstrings */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		17E1596C2E2698B2005F78C8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E159532E2698B0005F78C8 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E1595A2E2698B0005F78C8;
			remoteInfo = learnDemo;
		};
		17E159762E2698B2005F78C8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E159532E2698B0005F78C8 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E1595A2E2698B0005F78C8;
			remoteInfo = learnDemo;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		17321CA92E35E92700A3F272 /* Localizable.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = Localizable.xcstrings; sourceTree = "<group>"; };
		17321CB32E35F53300A3F272 /* InfoPlist.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = InfoPlist.xcstrings; sourceTree = "<group>"; };
		17E1595B2E2698B0005F78C8 /* learnDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = learnDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		17E1596B2E2698B2005F78C8 /* learnDemoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = learnDemoTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		17E159752E2698B2005F78C8 /* learnDemoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = learnDemoUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		17E1595D2E2698B0005F78C8 /* learnDemo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = learnDemo;
			sourceTree = "<group>";
		};
		17E1596E2E2698B2005F78C8 /* learnDemoTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = learnDemoTests;
			sourceTree = "<group>";
		};
		17E159782E2698B2005F78C8 /* learnDemoUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = learnDemoUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		17E159582E2698B0005F78C8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E159682E2698B2005F78C8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E159722E2698B2005F78C8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		17E159522E2698B0005F78C8 = {
			isa = PBXGroup;
			children = (
				17321CB32E35F53300A3F272 /* InfoPlist.xcstrings */,
				17321CA92E35E92700A3F272 /* Localizable.xcstrings */,
				17E1595D2E2698B0005F78C8 /* learnDemo */,
				17E1596E2E2698B2005F78C8 /* learnDemoTests */,
				17E159782E2698B2005F78C8 /* learnDemoUITests */,
				17E1595C2E2698B0005F78C8 /* Products */,
			);
			sourceTree = "<group>";
		};
		17E1595C2E2698B0005F78C8 /* Products */ = {
			isa = PBXGroup;
			children = (
				17E1595B2E2698B0005F78C8 /* learnDemo.app */,
				17E1596B2E2698B2005F78C8 /* learnDemoTests.xctest */,
				17E159752E2698B2005F78C8 /* learnDemoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		17E1595A2E2698B0005F78C8 /* learnDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E1597F2E2698B2005F78C8 /* Build configuration list for PBXNativeTarget "learnDemo" */;
			buildPhases = (
				17E159572E2698B0005F78C8 /* Sources */,
				17E159582E2698B0005F78C8 /* Frameworks */,
				17E159592E2698B0005F78C8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				17E1595D2E2698B0005F78C8 /* learnDemo */,
			);
			name = learnDemo;
			packageProductDependencies = (
			);
			productName = learnDemo;
			productReference = 17E1595B2E2698B0005F78C8 /* learnDemo.app */;
			productType = "com.apple.product-type.application";
		};
		17E1596A2E2698B2005F78C8 /* learnDemoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E159822E2698B2005F78C8 /* Build configuration list for PBXNativeTarget "learnDemoTests" */;
			buildPhases = (
				17E159672E2698B2005F78C8 /* Sources */,
				17E159682E2698B2005F78C8 /* Frameworks */,
				17E159692E2698B2005F78C8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17E1596D2E2698B2005F78C8 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17E1596E2E2698B2005F78C8 /* learnDemoTests */,
			);
			name = learnDemoTests;
			packageProductDependencies = (
			);
			productName = learnDemoTests;
			productReference = 17E1596B2E2698B2005F78C8 /* learnDemoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		17E159742E2698B2005F78C8 /* learnDemoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E159852E2698B2005F78C8 /* Build configuration list for PBXNativeTarget "learnDemoUITests" */;
			buildPhases = (
				17E159712E2698B2005F78C8 /* Sources */,
				17E159722E2698B2005F78C8 /* Frameworks */,
				17E159732E2698B2005F78C8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17E159772E2698B2005F78C8 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17E159782E2698B2005F78C8 /* learnDemoUITests */,
			);
			name = learnDemoUITests;
			packageProductDependencies = (
			);
			productName = learnDemoUITests;
			productReference = 17E159752E2698B2005F78C8 /* learnDemoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		17E159532E2698B0005F78C8 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					17E1595A2E2698B0005F78C8 = {
						CreatedOnToolsVersion = 16.2;
					};
					17E1596A2E2698B2005F78C8 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 17E1595A2E2698B0005F78C8;
					};
					17E159742E2698B2005F78C8 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 17E1595A2E2698B0005F78C8;
					};
				};
			};
			buildConfigurationList = 17E159562E2698B0005F78C8 /* Build configuration list for PBXProject "learnDemo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 17E159522E2698B0005F78C8;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 17E1595C2E2698B0005F78C8 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				17E1595A2E2698B0005F78C8 /* learnDemo */,
				17E1596A2E2698B2005F78C8 /* learnDemoTests */,
				17E159742E2698B2005F78C8 /* learnDemoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		17E159592E2698B0005F78C8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				17321CB42E35F53300A3F272 /* InfoPlist.xcstrings in Resources */,
				17321CAA2E35E92700A3F272 /* Localizable.xcstrings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E159692E2698B2005F78C8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E159732E2698B2005F78C8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		17E159572E2698B0005F78C8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E159672E2698B2005F78C8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E159712E2698B2005F78C8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		17E1596D2E2698B2005F78C8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17E1595A2E2698B0005F78C8 /* learnDemo */;
			targetProxy = 17E1596C2E2698B2005F78C8 /* PBXContainerItemProxy */;
		};
		17E159772E2698B2005F78C8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17E1595A2E2698B0005F78C8 /* learnDemo */;
			targetProxy = 17E159762E2698B2005F78C8 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		17E1597D2E2698B2005F78C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "$(NSCameraUsageDescription)";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		17E1597E2E2698B2005F78C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "$(NSCameraUsageDescription)";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		17E159802E2698B2005F78C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"learnDemo/Preview Content\"";
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.learnDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		17E159812E2698B2005F78C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"learnDemo/Preview Content\"";
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.learnDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		17E159832E2698B2005F78C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.learnDemoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/learnDemo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/learnDemo";
			};
			name = Debug;
		};
		17E159842E2698B2005F78C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.learnDemoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/learnDemo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/learnDemo";
			};
			name = Release;
		};
		17E159862E2698B2005F78C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.learnDemoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = learnDemo;
			};
			name = Debug;
		};
		17E159872E2698B2005F78C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.learnDemoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = learnDemo;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		17E159562E2698B0005F78C8 /* Build configuration list for PBXProject "learnDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E1597D2E2698B2005F78C8 /* Debug */,
				17E1597E2E2698B2005F78C8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E1597F2E2698B2005F78C8 /* Build configuration list for PBXNativeTarget "learnDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E159802E2698B2005F78C8 /* Debug */,
				17E159812E2698B2005F78C8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E159822E2698B2005F78C8 /* Build configuration list for PBXNativeTarget "learnDemoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E159832E2698B2005F78C8 /* Debug */,
				17E159842E2698B2005F78C8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E159852E2698B2005F78C8 /* Build configuration list for PBXNativeTarget "learnDemoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E159862E2698B2005F78C8 /* Debug */,
				17E159872E2698B2005F78C8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 17E159532E2698B0005F78C8 /* Project object */;
}
