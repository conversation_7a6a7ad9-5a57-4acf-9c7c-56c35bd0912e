//
//  CameraUITests.swift
//  learnDemoUITests
//
//  Camera functionality UI tests
//

import XCTest

final class CameraUITests: XCTestCase {
    
    var app: XCUIApplication!

    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launch()
    }

    override func tearDownWithError() throws {
        app = nil
    }

    // MARK: - Camera UI Tests
    @MainActor
    func testCameraButtonExists() throws {
        // Test that the camera button is present in the UI
        let cameraButton = app.buttons["拍照"]
        XCTAssertTrue(cameraButton.exists, "Camera button should exist in the UI")
    }
    
    @MainActor
    func testCameraButtonTap() throws {
        // Test tapping the camera button
        let cameraButton = app.buttons["拍照"]
        XCTAssertTrue(cameraButton.exists)
        
        if cameraButton.isEnabled {
            cameraButton.tap()
            
            // Check if permission alert appears or camera opens
            // Note: This test may behave differently on simulator vs device
            let permissionAlert = app.alerts.firstMatch
            let cameraInterface = app.otherElements["Camera"]
            
            // Either permission alert should appear or camera should open
            let expectation = expectation(description: "Camera interaction")
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                if permissionAlert.exists || cameraInterface.exists {
                    expectation.fulfill()
                } else {
                    // On simulator, camera might not be available
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    @MainActor
    func testScrollingFunctionality() throws {
        // Test that existing scrolling functionality still works
        let scrollView = app.scrollViews.firstMatch
        XCTAssertTrue(scrollView.exists, "Scroll view should exist")
        
        // Test scroll to top button
        let scrollToTopButton = app.buttons["滚动到顶部"]
        XCTAssertTrue(scrollToTopButton.exists)
        scrollToTopButton.tap()
        
        // Test scroll to middle button
        let scrollToMiddleButton = app.buttons["滚动到中间"]
        XCTAssertTrue(scrollToMiddleButton.exists)
        scrollToMiddleButton.tap()
        
        // Test scroll to bottom button
        let scrollToBottomButton = app.buttons["滚动到底部"]
        XCTAssertTrue(scrollToBottomButton.exists)
        scrollToBottomButton.tap()
    }
    
    @MainActor
    func testCameraStatusDisplay() throws {
        // Test that camera status is displayed
        let statusText = app.staticTexts.containing(NSPredicate(format: "label CONTAINS '摄像头状态:'")).firstMatch
        XCTAssertTrue(statusText.exists, "Camera status should be displayed")
    }
    
    @MainActor
    func testNavigationTitle() throws {
        // Test that navigation title is correct
        let navigationTitle = app.navigationBars["滚动测试"]
        XCTAssertTrue(navigationTitle.exists, "Navigation title should be '滚动测试'")
    }
    
    @MainActor
    func testCameraFunctionalitySection() throws {
        // Test that camera functionality section exists
        let cameraSection = app.staticTexts["摄像头功能"]
        XCTAssertTrue(cameraSection.exists, "Camera functionality section should exist")
    }
    
    @MainActor
    func testViewPhotoButtonAppearsAfterCapture() throws {
        // This test would require actually capturing a photo, which is complex in UI tests
        // For now, we'll just test that the button can appear
        let viewPhotoButton = app.buttons["查看照片"]
        
        // The button should not exist initially (no photo captured)
        // Note: This might exist if a photo was captured in a previous test run
        // In a real test environment, we'd reset the app state
    }
    
    // MARK: - Performance Tests
    @MainActor
    func testScrollPerformance() throws {
        let scrollView = app.scrollViews.firstMatch
        
        measure {
            scrollView.swipeUp()
            scrollView.swipeDown()
        }
    }
    
    // MARK: - Accessibility Tests
    @MainActor
    func testCameraButtonAccessibility() throws {
        let cameraButton = app.buttons["拍照"]
        XCTAssertTrue(cameraButton.exists)
        XCTAssertTrue(cameraButton.isHittable, "Camera button should be accessible")
    }
    
    @MainActor
    func testScrollButtonsAccessibility() throws {
        let buttons = ["滚动到顶部", "滚动到中间", "滚动到底部"]
        
        for buttonTitle in buttons {
            let button = app.buttons[buttonTitle]
            XCTAssertTrue(button.exists, "\(buttonTitle) button should exist")
            XCTAssertTrue(button.isHittable, "\(buttonTitle) button should be accessible")
        }
    }
}