# 设计文档

## 概述

摄像头拍照功能将集成到现有的SwiftUI应用中，提供完整的拍照和预览体验。该功能将使用SwiftUI的现代API和iOS的AVFoundation框架来实现摄像头访问、权限管理和图像捕获。

## 架构

### 核心组件架构
```
ContentView (主界面)
├── CameraButton (拍照按钮)
├── CameraView (摄像头界面) - Sheet呈现
│   ├── CameraPreview (摄像头预览)
│   ├── CaptureButton (拍照按钮)
│   └── CancelButton (取消按钮)
└── PhotoPreviewView (照片预览界面) - Sheet呈现
    ├── CapturedImage (预览图片)
    ├── RetakeButton (重拍按钮)
    └── KeepButton (保留按钮)
```

### 权限管理架构
```
CameraPermissionManager
├── checkPermissionStatus() -> AVAuthorizationStatus
├── requestPermission() -> Bool
└── openSettings() -> Void
```

## 组件和接口

### 1. CameraPermissionManager
负责处理所有摄像头权限相关的逻辑。

**接口：**
```swift
class CameraPermissionManager: ObservableObject {
    @Published var permissionStatus: AVAuthorizationStatus = .notDetermined
    
    func checkPermissionStatus() -> AVAuthorizationStatus
    func requestPermission() async -> Bool
    func openSettings()
}
```

### 2. CameraView
使用UIViewControllerRepresentable包装UIImagePickerController来提供摄像头功能。

**接口：**
```swift
struct CameraView: UIViewControllerRepresentable {
    @Binding var capturedImage: UIImage?
    @Binding var isPresented: Bool
    
    func makeUIViewController(context: Context) -> UIImagePickerController
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context)
    func makeCoordinator() -> Coordinator
}
```

### 3. PhotoPreviewView
显示拍摄的照片并提供重拍或保留选项。

**接口：**
```swift
struct PhotoPreviewView: View {
    let capturedImage: UIImage
    @Binding var isPresented: Bool
    let onRetake: () -> Void
    let onKeep: () -> Void
}
```

### 4. ContentView扩展
在现有ContentView中添加摄像头功能，保持现有滚动测试功能不变。

**新增状态变量：**
```swift
@State private var showCamera = false
@State private var showPhotoPreview = false
@State private var capturedImage: UIImage?
@StateObject private var cameraPermission = CameraPermissionManager()
```

## 数据模型

### CameraState枚举
```swift
enum CameraState {
    case idle           // 空闲状态
    case requesting     // 请求权限中
    case authorized     // 已授权
    case denied         // 权限被拒绝
    case restricted     // 权限受限
    case capturing      // 拍照中
    case previewing     // 预览中
}
```

### PermissionError枚举
```swift
enum PermissionError: LocalizedError {
    case denied
    case restricted
    case unknown
    
    var errorDescription: String? {
        switch self {
        case .denied:
            return "摄像头权限被拒绝，请在设置中启用"
        case .restricted:
            return "摄像头访问受限"
        case .unknown:
            return "未知权限错误"
        }
    }
}
```

## 错误处理

### 权限错误处理
1. **权限被拒绝**：显示Alert提示用户前往设置开启权限
2. **权限受限**：显示信息说明设备限制
3. **摄像头不可用**：显示错误消息并禁用功能

### 摄像头错误处理
1. **设备无摄像头**：检测设备能力并显示适当消息
2. **摄像头占用**：处理摄像头被其他应用占用的情况
3. **拍照失败**：提供重试选项

### 用户体验错误处理
1. **加载状态**：显示权限请求和摄像头初始化的加载状态
2. **错误恢复**：提供明确的错误恢复路径
3. **优雅降级**：在功能不可用时提供替代方案

## 测试策略

### 单元测试
1. **CameraPermissionManager测试**
   - 权限状态检查
   - 权限请求流程
   - 设置页面跳转

2. **数据模型测试**
   - CameraState状态转换
   - PermissionError错误消息

### 集成测试
1. **摄像头流程测试**
   - 完整拍照流程
   - 权限请求到拍照完成
   - 预览和重拍流程

2. **UI集成测试**
   - 按钮交互
   - Sheet呈现和关闭
   - 状态同步

### 用户界面测试
1. **权限流程测试**
   - 首次权限请求
   - 权限被拒绝后的设置跳转
   - 权限恢复后的功能可用性

2. **摄像头功能测试**
   - 摄像头界面呈现
   - 拍照功能
   - 预览界面操作

## 实现注意事项

### iOS权限配置
Info.plist中的摄像头使用说明已配置完成。

### SwiftUI最佳实践
1. 使用@StateObject管理权限状态
2. 使用sheet()修饰符呈现摄像头界面
3. 使用@Binding在组件间传递状态
4. 遵循SwiftUI的数据流原则

### 性能考虑
1. 延迟初始化摄像头组件
2. 适当的内存管理，及时释放图像资源
3. 异步处理权限请求避免阻塞UI

### 用户体验设计
1. 保持与现有应用风格一致
2. 提供清晰的视觉反馈
3. 优化触摸目标大小
4. 支持横竖屏切换