//
//  learnDemoTests.swift
//  learnDemoTests
//
//  Created by JackFan on 2025/7/15.
//

import XCTest
import AVFoundation
@testable import learnDemo

final class learnDemoTests: XCTestCase {
    
    var cameraPermissionManager: CameraPermissionManager!

    override func setUpWithError() throws {
        cameraPermissionManager = CameraPermissionManager()
    }

    override func tearDownWithError() throws {
        cameraPermissionManager = nil
    }

    // MARK: - Camera Permission Manager Tests
    func testCameraPermissionManagerInitialization() throws {
        XCTAssertNotNil(cameraPermissionManager)
        XCTAssertNotNil(cameraPermissionManager.permissionStatus)
        XCTAssertNotNil(cameraPermissionManager.cameraState)
    }
    
    func testCheckPermissionStatus() throws {
        let status = cameraPermissionManager.checkPermissionStatus()
        XCTAssertTrue([
            AVAuthorizationStatus.authorized,
            .denied,
            .restricted,
            .notDetermined
        ].contains(status))
    }
    
    func testCameraAvailability() throws {
        let isAvailable = cameraPermissionManager.isCameraAvailable()
        // This will be true on physical devices, may be false on simulator
        XCTAssertTrue(isAvailable || !isAvailable) // Just ensure it returns a boolean
    }
    
    func testPermissionErrorHandling() throws {
        // Test denied permission error
        cameraPermissionManager.permissionStatus = .denied
        let deniedError = cameraPermissionManager.getPermissionError()
        XCTAssertNotNil(deniedError)
        XCTAssertEqual(deniedError?.localizedDescription, "摄像头权限被拒绝，请在设置中启用")
        
        // Test restricted permission error
        cameraPermissionManager.permissionStatus = .restricted
        let restrictedError = cameraPermissionManager.getPermissionError()
        XCTAssertNotNil(restrictedError)
        XCTAssertEqual(restrictedError?.localizedDescription, "摄像头访问受限")
        
        // Test authorized permission (no error)
        cameraPermissionManager.permissionStatus = .authorized
        let noError = cameraPermissionManager.getPermissionError()
        XCTAssertNil(noError)
    }
    
    // MARK: - Camera State Tests
    func testCameraStateTransitions() throws {
        // Test initial state
        XCTAssertEqual(cameraPermissionManager.cameraState, .idle)
        
        // Simulate permission request
        cameraPermissionManager.cameraState = .requesting
        XCTAssertEqual(cameraPermissionManager.cameraState, .requesting)
        
        // Simulate authorized state
        cameraPermissionManager.cameraState = .authorized
        XCTAssertEqual(cameraPermissionManager.cameraState, .authorized)
    }
    
    // MARK: - Permission Error Tests
    func testPermissionErrorTypes() throws {
        let deniedError = PermissionError.denied
        XCTAssertEqual(deniedError.localizedDescription, "摄像头权限被拒绝，请在设置中启用")
        
        let restrictedError = PermissionError.restricted
        XCTAssertEqual(restrictedError.localizedDescription, "摄像头访问受限")
        
        let unknownError = PermissionError.unknown
        XCTAssertEqual(unknownError.localizedDescription, "未知权限错误")
    }
    
    // MARK: - Camera View Tests
    func testCameraViewAvailability() throws {
        let isAvailable = CameraView.isCameraAvailable()
        // This should match UIImagePickerController availability
        XCTAssertEqual(isAvailable, UIImagePickerController.isSourceTypeAvailable(.camera))
    }
    
    func testCameraAuthorizationStatus() throws {
        let status = CameraView.getCameraAuthorizationStatus()
        XCTAssertTrue([
            AVAuthorizationStatus.authorized,
            .denied,
            .restricted,
            .notDetermined
        ].contains(status))
    }
    
    // MARK: - Integration Tests
    func testCameraPermissionFlow() async throws {
        // Test permission request flow
        let initialStatus = cameraPermissionManager.checkPermissionStatus()
        
        if initialStatus == .notDetermined {
            // Test permission request
            let granted = await cameraPermissionManager.requestPermission()
            let newStatus = cameraPermissionManager.checkPermissionStatus()
            
            if granted {
                XCTAssertEqual(newStatus, .authorized)
                XCTAssertEqual(cameraPermissionManager.cameraState, .authorized)
            } else {
                XCTAssertTrue([AVAuthorizationStatus.denied, .restricted].contains(newStatus))
            }
        }
    }
    
    func testErrorHandlingFlow() throws {
        // Test error handling for different permission states
        let testCases: [(AVAuthorizationStatus, CameraState)] = [
            (.authorized, .authorized),
            (.denied, .denied),
            (.restricted, .restricted),
            (.notDetermined, .idle)
        ]
        
        for (status, expectedState) in testCases {
            cameraPermissionManager.permissionStatus = status
            // Simulate the state update that would happen in the real flow
            switch status {
            case .authorized:
                cameraPermissionManager.cameraState = .authorized
            case .denied:
                cameraPermissionManager.cameraState = .denied
            case .restricted:
                cameraPermissionManager.cameraState = .restricted
            case .notDetermined:
                cameraPermissionManager.cameraState = .idle
            @unknown default:
                cameraPermissionManager.cameraState = .idle
            }
            
            XCTAssertEqual(cameraPermissionManager.cameraState, expectedState)
        }
    }
    
    // MARK: - Performance Tests
    func testPermissionCheckPerformance() throws {
        measure {
            _ = cameraPermissionManager.checkPermissionStatus()
        }
    }
    
    func testCameraAvailabilityPerformance() throws {
        measure {
            _ = cameraPermissionManager.isCameraAvailable()
        }
    }
}
